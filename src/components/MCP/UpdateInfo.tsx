import {Flex, Typography, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {css, cx} from '@emotion/css';
import {CSSProperties} from 'react';
import UserAvatar from '@/design/UserAvatar';
import {overflowHiddenCss} from '@/styles/components';
import {formatISOTime} from '@/utils/date';
import {useUserBaseInfo} from '@/design/MCP/useUserBaseInfo';

const Text = styled(Typography.Text)`
    font-size: 12px;
    line-height: 20px;
`;

const flexZero = css`
    flex-shrink: 0;
    flex-grow: 0;
`;
interface Props {
    className?: string;
    color?: string;
    username?: string;
    time?: string;
    style?: CSSProperties;
    variant?: 'full' | 'space-card';
    showAvatar?: boolean;
}

export default function UpdateInfo({
    className,
    username,
    time,
    color = '#545454',
    style,
    variant = 'full',
    showAvatar = true,
}: Props) {
    const dateOnly = formatISOTime(time, 'yyyy-MM-dd');
    const fullDateTime = formatISOTime(time, 'yyyy-MM-dd HH:mm:ss');
    const userInfo = useUserBaseInfo(username);
    return (
        <Flex className={cx(overflowHiddenCss, className)} style={style} align="center" gap={4}>
            {showAvatar && <UserAvatar username={username} iconSize={18} className={flexZero} showChineseName />}
            {variant === 'space-card' ? (
                <Text ellipsis style={{color}}>
                    {userInfo?.chineseName ?? username} 更新于{' '}
                    <Tooltip title={fullDateTime}>
                        <span style={{cursor: 'pointer'}}>{dateOnly}</span>
                    </Tooltip>
                </Text>
            ) : (
                <Text ellipsis style={{color}}>
                    {userInfo?.chineseName ?? username} 更新于 {fullDateTime}
                </Text>
            )}
        </Flex>
    );
}
