import styled from '@emotion/styled';
import {Flex, FlexProps} from 'antd';
import bg from '@/assets/mcp/cardBg.png';
import vipbg from '@/assets/mcp/cardVipBg.png';

const Container = styled(Flex)<{official?: boolean}>`
    position: relative;
    cursor: pointer;
    background-image: url(${({official}) => (official ? vipbg : bg)});
    background-repeat: no-repeat;
    background-size: contain;
    border: 1px solid rgba(75, 108, 159, 0.15);
    border-radius: 6px;
    color: #545454;
    overflow: hidden;
    :hover {
        border-color: #0080FF;
    }
`;

interface Props extends FlexProps {
    official?: boolean;
}
export default function MCPCard({children, onClick, style, className, vertical, official}: Props) {
    return (
        <Container
            onClick={onClick}
            vertical={vertical}
            style={style}
            className={className}
            official={official}
        >
            {children}
        </Container>
    );
}
